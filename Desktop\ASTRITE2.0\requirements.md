# Requirements Document

## Introduction

This feature involves creating <PERSON><PERSON><PERSON>, an AI-powered file auto-organizer and duplicate killer for Windows. The application focuses on two core functions: intelligently categorizing and organizing files using local AI, and detecting/removing duplicate files with high accuracy. The goal is to ship an MVP that excels at these specific tasks rather than being a generic system cleaner.

## Requirements

### Requirement 1

**User Story:** As a Windows user with messy folders, I want to easily add folders to <PERSON>tri<PERSON> and organize them with one click, so that I can manage multiple directories like Downloads, Desktop, and Documents from a central dashboard.

#### Acceptance Criteria

1. WHEN the user clicks "Add Folder" THEN the system SHALL open a folder picker dialog to select directories to manage
2. WHEN folders are added THEN the system SHALL display them in a dashboard with statistics (file count, last organized, status)
3. WHEN the user clicks "Organize" on a folder THEN the system SHALL scan all files and use local AI (Mistral model) to categorize into 8 categories: Docs, Images, Videos, Audio, Archives, Installers, Code, Misc
4. WHEN categorization is complete THEN the system SHALL move files to `C:\Users\<USER>\Astrite\{Category}\{YYYY-MM}\{File}` structure automatically
5. IF the AI model fails to categorize a file THEN the system SHALL fall back to extension-based rules for classification

### Requirement 2

**User Story:** As a user concerned about accuracy, I want to preview and edit the AI's categorization decisions before files are moved, so that I can ensure important files go to the right places.

#### Acceptance Criteria

1. WHEN files are categorized THEN the system SHALL display a preview showing proposed file movements with editable categories
2. WHEN the user views the preview THEN the system SHALL show file thumbnails, names, and proposed destination folders
3. WHEN the user disagrees with a categorization THEN the system SHALL allow manual category reassignment before applying changes
4. WHEN the user approves the organization plan THEN the system SHALL execute all file movements with live progress feedback

### Requirement 3

**User Story:** As a user with limited storage space, I want intelligent duplicate detection that finds exact and similar files across my entire system, so that I can safely remove redundant files and reclaim disk space.

#### Acceptance Criteria

1. WHEN the user initiates duplicate scanning THEN the system SHALL use xxHash for exact duplicates and pHash for perceptual duplicates on images/videos
2. WHEN duplicates are found THEN the system SHALL group them with preview thumbnails and automatically recommend which copy to keep as master
3. WHEN the user reviews duplicate groups THEN the system SHALL provide one-click deletion of all copies except the master
4. IF duplicate scanning encounters large drives THEN the system SHALL handle the process without crashing and provide cancelable progress updates

### Requirement 4

**User Story:** As a cautious user, I want comprehensive undo capabilities for all file operations, so that I can safely experiment with organization and duplicate removal knowing I can reverse any mistakes.

#### Acceptance Criteria

1. WHEN any file operation is performed THEN the system SHALL log the action with original paths, timestamps, and file hashes to SQLite database
2. WHEN the user accesses undo history THEN the system SHALL display all operations within the last 90 days with search and filter capabilities
3. WHEN the user selects operations to undo THEN the system SHALL restore files to their original locations reliably
4. IF the system is rebooted THEN the undo functionality SHALL remain available and support restoration of large batches (50k+ files) without data loss

### Requirement 5

**User Story:** As a performance-conscious user, I want the application to handle large file sets efficiently, so that I can organize thousands of files without long wait times or system slowdowns.

#### Acceptance Criteria

1. WHEN processing large file sets THEN the system SHALL achieve 95% categorization accuracy on mixed collections of 1,000+ files
2. WHEN scanning for duplicates THEN the system SHALL complete processing of 10,000 files in under 30 seconds on mid-range hardware
3. WHEN performing any operation THEN the system SHALL use async processing to keep the UI responsive and allow operation cancellation
4. IF the system encounters performance bottlenecks THEN the system SHALL leverage available CPU/GPU resources for AI inference and parallel processing

### Requirement 6

**User Story:** As a user managing multiple folders, I want to see the status and statistics of all my managed folders in one dashboard, so that I can track organization progress and decide which folders need attention.

#### Acceptance Criteria

1. WHEN the user opens the application THEN the system SHALL display a dashboard showing all added folders with key statistics
2. WHEN displaying folder statistics THEN the system SHALL show file count, last organized date, and organization status
3. WHEN the user wants to remove a folder THEN the system SHALL allow removal from management without affecting the actual files
4. WHEN folders are organized THEN the system SHALL update the dashboard statistics in real-time

### Requirement 7

**User Story:** As a privacy-focused user, I want all AI processing to happen locally on my machine, so that my files and their contents never leave my system or require internet connectivity.

#### Acceptance Criteria

1. WHEN the application starts THEN the system SHALL run entirely offline using local Mistral model via Ollama with <50MB runtime footprint
2. WHEN processing files THEN the system SHALL perform all AI inference on local CPU/GPU without any network communication
3. WHEN telemetry is collected THEN the system SHALL only gather opt-in error reporting data and never transmit file contents or paths
4. IF the local AI model is unavailable THEN the system SHALL gracefully fall back to extension-based categorization without degrading core functionality