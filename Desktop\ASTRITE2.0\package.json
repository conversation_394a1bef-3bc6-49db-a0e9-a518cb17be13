{"name": "astrite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.18.2", "vite": "^7.0.4"}}